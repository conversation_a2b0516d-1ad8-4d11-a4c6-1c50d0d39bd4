#include <iostream>
#include <vector>
#include <thread>
#include <string>
#include <chrono>

// 演示类，用于观察构造过程
class MyClass {
private:
    std::string name;
    int value;
    
public:
    // 构造函数
    MyClass(const std::string& n, int v) : name(n), value(v) {
        std::cout << "构造 MyClass(" << name << ", " << value << ")\n";
    }
    
    // 拷贝构造函数
    MyClass(const MyClass& other) : name(other.name), value(other.value) {
        std::cout << "拷贝构造 MyClass(" << name << ", " << value << ")\n";
    }
    
    // 移动构造函数
    MyClass(MyClass&& other) noexcept : name(std::move(other.name)), value(other.value) {
        std::cout << "移动构造 MyClass(" << name << ", " << value << ")\n";
    }
    
    // 析构函数
    ~MyClass() {
        std::cout << "析构 MyClass(" << name << ", " << value << ")\n";
    }
    
    void display() const {
        std::cout << "MyClass: " << name << " = " << value << "\n";
    }
};

void simple_task(int id) {
    std::cout << "线程 " << id << " 执行中...\n";
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    std::cout << "线程 " << id << " 完成\n";
}

int main() {
    std::cout << "=== emplace_back vs push_back 演示 ===\n\n";
    
    // 1. 对于普通对象的比较
    std::cout << "1. 普通对象 - push_back vs emplace_back:\n";
    
    std::vector<MyClass> vec1;
    std::cout << "\n使用 push_back:\n";
    MyClass a  = MyClass("Object1", 100);
    vec1.push_back(a);  // 先构造，再移动
    
    std::vector<MyClass> vec2;
    std::cout << "\n使用 emplace_back:\n";
    vec2.emplace_back("Object2", 200);  // 直接在容器中构造
    
    std::cout << "\n--- 可以看到 emplace_back 更高效，少了一次构造 ---\n\n";
    
    // 2. 对于 std::thread 的使用
    std::cout << "2. std::thread 的使用:\n";
    
    std::vector<std::thread> threads;
    
    std::cout << "\n使用 emplace_back 创建线程:\n";
    // emplace_back 直接在 vector 中构造 thread 对象
    threads.emplace_back(simple_task, 1);
    threads.emplace_back(simple_task, 2);
    
    std::cout << "\n如果使用 push_back 会怎样?\n";
    // 这样做会有问题，因为 std::thread 不能拷贝！
    // threads.push_back(std::thread(simple_task, 3));  // 编译错误！
    
    // 必须使用 std::move
    threads.push_back(std::move(std::thread(simple_task, 3)));
    
    std::cout << "\n等待所有线程完成...\n";
    for (auto& t : threads) {
        t.join();
    }
    
    // 3. emplace_back 的参数传递
    std::cout << "\n3. emplace_back 参数传递演示:\n";
    
    std::vector<std::thread> more_threads;
    
    // emplace_back 可以接受构造函数的参数
    more_threads.emplace_back([]() {
        std::cout << "Lambda 线程执行\n";
    });
    
    // 传递多个参数
    more_threads.emplace_back([](const std::string& msg, int count) {
        for (int i = 0; i < count; ++i) {
            std::cout << msg << " " << (i + 1) << "\n";
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }, "消息", 3);
    
    // 等待完成
    for (auto& t : more_threads) {
        t.join();
    }
    
    // 4. 性能对比总结
    std::cout << "\n=== 总结 ===\n";
    std::cout << "emplace_back 的优势:\n";
    std::cout << "1. 就地构造，避免额外的拷贝/移动\n";
    std::cout << "2. 对于不可拷贝的对象（如thread）更方便\n";
    std::cout << "3. 参数直接传递给构造函数\n";
    std::cout << "4. 通常性能更好\n";
    
    std::cout << "\n何时使用:\n";
    std::cout << "- 优先使用 emplace_back\n";
    std::cout << "- 特别是对于复杂对象或不可拷贝对象\n";
    std::cout << "- push_back 主要用于已有对象的插入\n";
    
    return 0;
}
