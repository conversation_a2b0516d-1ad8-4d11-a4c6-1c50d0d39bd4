#include <iostream>
#include <future>
#include <thread>
#include <chrono>
#include <vector>
#include <random>
#include <exception>

// 模拟一个耗时的计算任务
int expensive_calculation(int n) {
    std::cout << "开始计算 " << n << " 的阶乘...\n";
    
    // 模拟耗时操作
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    if (n < 0) {
        throw std::invalid_argument("负数没有阶乘");
    }
    
    long long result = 1;
    for (int i = 1; i <= n; ++i) {
        result *= i;
        if (result > 1000000) { // 防止溢出
            throw std::overflow_error("结果太大");
        }
    }
    
    std::cout << n << " 的阶乘计算完成\n";
    return static_cast<int>(result);
}

// 模拟异步数据获取
std::string fetch_data(const std::string& source) {
    std::cout << "从 " << source << " 获取数据...\n";
    
    // 模拟网络延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(800 + rand() % 500));
    
    return "来自 " + source + " 的数据";
}

// 使用 promise 和 future 进行线程间通信
void producer(std::promise<int> promise, int value) {
    std::cout << "生产者开始工作...\n";
    
    // 模拟工作
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    
    // 设置结果
    promise.set_value(value * value);
    std::cout << "生产者完成工作，设置值: " << value * value << "\n";
}

int main() {
    std::cout << "=== C++ Future 和 Promise 示例 ===\n\n";
    
    // 1. 使用 std::async 进行异步计算
    std::cout << "1. 使用 std::async 进行异步计算:\n";
    
    // 启动异步任务
    auto future1 = std::async(std::launch::async, expensive_calculation, 5);
    auto future2 = std::async(std::launch::async, expensive_calculation, 6);
    
    std::cout << "异步任务已启动，主线程可以做其他工作...\n";
    
    // 主线程做其他工作
    for (int i = 0; i < 3; ++i) {
        std::cout << "主线程工作 " << (i + 1) << "\n";
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
    
    // 获取结果
    try {
        int result1 = future1.get();
        int result2 = future2.get();
        std::cout << "结果: " << result1 << " 和 " << result2 << "\n";
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << "\n";
    }
    
    std::cout << "\n2. 使用 std::async 处理多个数据源:\n";
    
    // 2. 并行获取多个数据源
    std::vector<std::future<std::string>> data_futures;
    std::vector<std::string> sources = {"数据库", "API", "文件系统", "缓存"};
    
    // 启动所有异步任务
    for (const auto& source : sources) {
        data_futures.push_back(
            std::async(std::launch::async, fetch_data, source)
        );
    }
    
    // 收集所有结果
    std::cout << "收集数据结果:\n";
    for (auto& future : data_futures) {
        std::cout << "- " << future.get() << "\n";
    }
    
    std::cout << "\n3. 使用 Promise 和 Future 进行线程间通信:\n";
    
    // 3. Promise 和 Future 示例
    std::promise<int> promise;
    std::future<int> future = promise.get_future();
    
    // 启动生产者线程
    std::thread producer_thread(producer, std::move(promise), 10);
    
    std::cout << "消费者等待结果...\n";
    
    // 等待结果（可以设置超时）
    if (future.wait_for(std::chrono::seconds(2)) == std::future_status::ready) {
        int result = future.get();
        std::cout << "消费者收到结果: " << result << "\n";
    } else {
        std::cout << "超时！\n";
    }
    
    producer_thread.join();
    
    std::cout << "\n4. 处理异步任务中的异常:\n";
    
    // 4. 异常处理示例
    auto future_with_exception = std::async(std::launch::async, expensive_calculation, -1);
    
    try {
        int result = future_with_exception.get();
        std::cout << "结果: " << result << "\n";
    } catch (const std::exception& e) {
        std::cout << "捕获异常: " << e.what() << "\n";
    }
    
    std::cout << "\n5. 使用 std::packaged_task:\n";
    
    // 5. packaged_task 示例
    std::packaged_task<int(int)> task(expensive_calculation);
    std::future<int> task_future = task.get_future();
    
    // 在另一个线程中运行任务
    std::thread task_thread(std::move(task), 4);
    
    std::cout << "等待 packaged_task 完成...\n";
    int task_result = task_future.get();
    std::cout << "packaged_task 结果: " << task_result << "\n";
    
    task_thread.join();
    
    std::cout << "\n=== Future 和 Promise 示例完成 ===\n";
    
    return 0;
}
