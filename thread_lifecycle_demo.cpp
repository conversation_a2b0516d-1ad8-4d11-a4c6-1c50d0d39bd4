#include <iostream>
#include <thread>
#include <chrono>
#include <mutex>

std::mutex print_mutex;

void worker_function(int id) {
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "线程 " << id << " 开始执行 (时间点A)\n";
    }
    
    // 模拟工作
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "线程 " << id << " 完成工作 (时间点B)\n";
    }
}

int main() {
    std::cout << "=== 线程生命周期演示 ===\n\n";
    
    std::cout << "主线程: 准备创建线程...\n";
    
    // 1. 线程声明并立即开始执行
    std::cout << "主线程: 创建线程1 - 注意线程立即开始执行！\n";
    std::thread t1(worker_function, 1);
    
    std::cout << "主线程: 线程1已创建，继续执行主线程代码...\n";
    
    // 主线程继续做其他事情
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    std::cout << "主线程: 做一些其他工作...\n";
    
    std::cout << "主线程: 创建线程2\n";
    std::thread t2(worker_function, 2);
    
    std::cout << "主线程: 两个线程都在并发执行中...\n";
    
    // 2. 演示线程的并发执行
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    std::cout << "主线程: 继续执行，线程仍在后台运行...\n";
    
    // 3. 等待线程完成
    std::cout << "主线程: 现在等待线程完成...\n";
    t1.join();  // 等待线程1完成
    std::cout << "主线程: 线程1已完成\n";
    
    t2.join();  // 等待线程2完成
    std::cout << "主线程: 线程2已完成\n";
    
    std::cout << "\n=== 对比：延迟启动的模拟 ===\n";
    
    // 4. 模拟"延迟启动"的效果（实际上C++没有这个功能）
    std::cout << "如果线程不是立即执行的话...\n";
    
    std::cout << "步骤1: 声明线程对象（假设不立即执行）\n";
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    std::cout << "步骤2: 手动启动线程（假设的API）\n";
    std::thread t3(worker_function, 3);  // 实际上还是立即执行
    
    std::cout << "但实际上C++中线程一声明就开始执行了！\n";
    
    t3.join();
    
    std::cout << "\n=== 重要概念总结 ===\n";
    std::cout << "1. std::thread 构造 = 立即启动\n";
    std::cout << "2. 线程并发执行，不会阻塞主线程\n";
    std::cout << "3. join() 用于等待线程完成\n";
    std::cout << "4. detach() 用于分离线程\n";
    
    return 0;
}
