#include <iostream>
#include <thread>
#include <vector>
#include <queue>
#include <functional>
#include <mutex>
#include <condition_variable>
#include <future>
#include <chrono>

// 简单的线程池实现
class ThreadPool {
private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    std::mutex queue_mutex;
    std::condition_variable condition;
    bool stop;
    
public:
    ThreadPool(size_t num_threads) : stop(false) {
        // 创建工作线程
        for (size_t i = 0; i < num_threads; ++i) {
            workers.emplace_back([this, i] {
                std::cout << "工作线程 " << i << " 启动\n";
                
                while (true) {
                    std::function<void()> task;
                    
                    {
                        std::unique_lock<std::mutex> lock(queue_mutex);
                        
                        // 等待任务或停止信号
                        condition.wait(lock, [this] { return stop || !tasks.empty(); });
                        
                        if (stop && tasks.empty()) {
                            std::cout << "工作线程 " << i << " 退出\n";
                            return;
                        }
                        
                        // 获取任务
                        task = std::move(tasks.front());
                        tasks.pop();
                    }
                    
                    // 执行任务
                    task();
                }
            });
        }
    }
    
    // 添加任务到线程池
    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        
        using return_type = typename std::result_of<F(Args...)>::type;
        
        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        std::future<return_type> result = task->get_future();
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            
            if (stop) {
                throw std::runtime_error("线程池已停止");
            }
            
            tasks.emplace([task]() { (*task)(); });
        }
        
        condition.notify_one();
        return result;
    }
    
    // 获取队列中的任务数量
    size_t get_queue_size() {
        std::lock_guard<std::mutex> lock(queue_mutex);
        return tasks.size();
    }
    
    // 析构函数 - 停止所有线程
    ~ThreadPool() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            stop = true;
        }
        
        condition.notify_all();
        
        for (std::thread& worker : workers) {
            worker.join();
        }
        
        std::cout << "线程池已关闭\n";
    }
};

// 示例任务函数
int compute_task(int id, int duration_ms) {
    std::cout << "任务 " << id << " 开始执行 (持续 " << duration_ms << "ms)\n";
    
    std::this_thread::sleep_for(std::chrono::milliseconds(duration_ms));
    
    int result = id * id;
    std::cout << "任务 " << id << " 完成，结果: " << result << "\n";
    
    return result;
}

// 字符串处理任务
std::string process_string(const std::string& input, int repeat) {
    std::cout << "处理字符串: \"" << input << "\" (重复 " << repeat << " 次)\n";
    
    std::string result;
    for (int i = 0; i < repeat; ++i) {
        result += input + " ";
    }
    
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    std::cout << "字符串处理完成\n";
    return result;
}

// 模拟文件处理任务
void file_processing_task(const std::string& filename) {
    std::cout << "开始处理文件: " << filename << "\n";
    
    // 模拟文件处理时间
    std::this_thread::sleep_for(std::chrono::milliseconds(300 + rand() % 500));
    
    std::cout << "文件 " << filename << " 处理完成\n";
}

int main() {
    std::cout << "=== C++ 线程池示例 ===\n\n";
    
    // 创建有4个工作线程的线程池
    ThreadPool pool(4);
    
    std::cout << "1. 提交计算任务:\n";
    
    // 1. 提交多个计算任务
    std::vector<std::future<int>> compute_results;
    
    for (int i = 1; i <= 8; ++i) {
        auto future = pool.enqueue(compute_task, i, 500 + i * 100);
        compute_results.push_back(std::move(future));
    }
    
    std::cout << "已提交 8 个计算任务，队列大小: " << pool.get_queue_size() << "\n\n";
    
    // 等待并收集计算结果
    std::cout << "收集计算结果:\n";
    for (size_t i = 0; i < compute_results.size(); ++i) {
        int result = compute_results[i].get();
        std::cout << "任务 " << (i + 1) << " 结果: " << result << "\n";
    }
    
    std::cout << "\n2. 提交字符串处理任务:\n";
    
    // 2. 提交字符串处理任务
    std::vector<std::future<std::string>> string_results;
    std::vector<std::string> inputs = {"Hello", "World", "C++", "Thread", "Pool"};
    
    for (size_t i = 0; i < inputs.size(); ++i) {
        auto future = pool.enqueue(process_string, inputs[i], static_cast<int>(i + 2));
        string_results.push_back(std::move(future));
    }
    
    // 收集字符串处理结果
    std::cout << "字符串处理结果:\n";
    for (size_t i = 0; i < string_results.size(); ++i) {
        std::string result = string_results[i].get();
        std::cout << "结果 " << (i + 1) << ": " << result << "\n";
    }
    
    std::cout << "\n3. 提交文件处理任务 (无返回值):\n";
    
    // 3. 提交无返回值的任务
    std::vector<std::future<void>> file_futures;
    std::vector<std::string> filenames = {
        "document1.txt", "image2.jpg", "data3.csv", 
        "config4.xml", "log5.txt", "backup6.zip"
    };
    
    for (const auto& filename : filenames) {
        auto future = pool.enqueue(file_processing_task, filename);
        file_futures.push_back(std::move(future));
    }
    
    // 等待所有文件处理完成
    for (auto& future : file_futures) {
        future.get(); // 等待任务完成
    }
    
    std::cout << "\n4. 使用 lambda 表达式提交任务:\n";
    
    // 4. 使用 lambda 表达式
    auto lambda_future = pool.enqueue([](int a, int b) -> int {
        std::cout << "Lambda 任务: 计算 " << a << " + " << b << "\n";
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
        int result = a + b;
        std::cout << "Lambda 任务完成，结果: " << result << "\n";
        return result;
    }, 10, 20);
    
    int lambda_result = lambda_future.get();
    std::cout << "Lambda 任务最终结果: " << lambda_result << "\n";
    
    std::cout << "\n5. 批量任务处理:\n";
    
    // 5. 批量提交任务并等待全部完成
    std::vector<std::future<void>> batch_futures;
    
    for (int i = 1; i <= 10; ++i) {
        batch_futures.push_back(pool.enqueue([i]() {
            std::cout << "批量任务 " << i << " 执行中...\n";
            std::this_thread::sleep_for(std::chrono::milliseconds(100 + i * 50));
            std::cout << "批量任务 " << i << " 完成\n";
        }));
    }
    
    std::cout << "等待所有批量任务完成...\n";
    for (auto& future : batch_futures) {
        future.get();
    }
    
    std::cout << "\n=== 线程池示例完成 ===\n";
    std::cout << "线程池将在程序结束时自动关闭...\n";
    
    return 0;
}
