cmake_minimum_required(VERSION 3.10)
project(CppConcurrencyExamples)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找线程库
find_package(Threads REQUIRED)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")

# 创建可执行文件
add_executable(basic_thread_example basic_thread_example.cpp)
add_executable(future_promise_example future_promise_example.cpp)
add_executable(thread_synchronization thread_synchronization.cpp)
add_executable(thread_pool_example thread_pool_example.cpp)

# 链接线程库
target_link_libraries(basic_thread_example Threads::Threads)
target_link_libraries(future_promise_example Threads::Threads)
target_link_libraries(thread_synchronization Threads::Threads)
target_link_libraries(thread_pool_example Threads::Threads)

# 设置输出目录
set_target_properties(basic_thread_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
set_target_properties(future_promise_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
set_target_properties(thread_synchronization PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
set_target_properties(thread_pool_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
