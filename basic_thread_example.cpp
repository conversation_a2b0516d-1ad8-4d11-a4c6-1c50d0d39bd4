#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <mutex>

// 全局互斥锁，用于保护共享资源
std::mutex print_mutex;

// 简单的线程函数
void simple_task(int id) {
    // 使用锁保护输出，避免输出混乱
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "线程 " << id << " 开始执行\n";
    }
    
    // 模拟一些工作
    std::this_thread::sleep_for(std::chrono::milliseconds(1000 + id * 100));
    
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "线程 " << id << " 完成工作\n";
    }
}

// 带参数的线程函数
void worker_with_params(const std::string& name, int count) {
    for (int i = 0; i < count; ++i) {
        {
            std::lock_guard<std::mutex> lock(print_mutex);
            std::cout << name << " 执行第 " << (i + 1) << " 次任务\n";
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}

// 使用类成员函数作为线程函数
class TaskRunner {
public:
    void run_task(int task_id) {
        {
            std::lock_guard<std::mutex> lock(print_mutex);
            std::cout << "TaskRunner 执行任务 " << task_id << "\n";
        }
        
        // 模拟工作
        std::this_thread::sleep_for(std::chrono::milliseconds(800));
        
        {
            std::lock_guard<std::mutex> lock(print_mutex);
            std::cout << "TaskRunner 完成任务 " << task_id << "\n";
        }
    }
};

int main() {
    std::cout << "=== C++ Thread 基础示例 ===\n\n";
    
    // 1. 创建和启动基本线程
    std::cout << "1. 基本线程创建和执行:\n";
    std::vector<std::thread> threads;
    
    // 创建多个线程
    for (int i = 1; i <= 3; ++i) {
        threads.push_back(std::thread(simple_task, i));
    }
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    // std::cout << "\n2. 带参数的线程:\n";
    //
    // // 2. 创建带参数的线程
    // std::thread worker1(worker_with_params, "Worker-A", 3);
    // std::thread worker2(worker_with_params, "Worker-B", 2);
    //
    // worker1.join();
    // worker2.join();
    //
    // std::cout << "\n3. 使用类成员函数作为线程函数:\n";
    //
    // // 3. 使用类成员函数
    // TaskRunner runner;
    // std::thread class_thread(&TaskRunner::run_task, &runner, 100);
    // class_thread.join();
    //
    // std::cout << "\n4. 使用 lambda 表达式:\n";
    //
    // // 4. 使用 lambda 表达式
    // std::thread lambda_thread([](int value) {
    //     std::lock_guard<std::mutex> lock(print_mutex);
    //     std::cout << "Lambda 线程处理值: " << value << "\n";
    // }, 42);
    //
    // lambda_thread.join();
    //
    // std::cout << "\n5. 线程分离 (detach) 示例:\n";
    //
    // // 5. 线程分离示例
    // std::thread detached_thread([]() {
    //     std::this_thread::sleep_for(std::chrono::milliseconds(100));
    //     std::lock_guard<std::mutex> lock(print_mutex);
    //     std::cout << "分离的线程执行完成\n";
    // });
    //
    // detached_thread.detach(); // 分离线程，主线程不需要等待
    //
    // // 等待一下，确保分离的线程有时间执行
    // std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    std::cout << "\n=== 所有线程示例完成 ===\n";
    
    return 0;
}
