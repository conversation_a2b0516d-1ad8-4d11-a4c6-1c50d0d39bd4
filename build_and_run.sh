#!/bin/bash

# C++ 并发编程示例 - 构建和运行脚本

echo "=== C++ 并发编程示例构建和运行脚本 ==="
echo

# 检查编译器
if ! command -v g++ &> /dev/null; then
    echo "错误: 未找到 g++ 编译器"
    exit 1
fi

# 检查C++17支持
echo "检查编译器版本..."
g++ --version | head -1

echo
echo "=== 开始编译示例程序 ==="

# 编译选项
CXX_FLAGS="-std=c++17 -pthread -Wall -Wextra -O2"

# 编译所有示例
echo "1. 编译基础线程示例..."
if g++ $CXX_FLAGS -o basic_thread_example basic_thread_example.cpp; then
    echo "   ✓ basic_thread_example 编译成功"
else
    echo "   ✗ basic_thread_example 编译失败"
    exit 1
fi

echo "2. 编译Future和Promise示例..."
if g++ $CXX_FLAGS -o future_promise_example future_promise_example.cpp; then
    echo "   ✓ future_promise_example 编译成功"
else
    echo "   ✗ future_promise_example 编译失败"
    exit 1
fi

echo "3. 编译线程同步示例..."
if g++ $CXX_FLAGS -o thread_synchronization thread_synchronization.cpp; then
    echo "   ✓ thread_synchronization 编译成功"
else
    echo "   ✗ thread_synchronization 编译失败"
    exit 1
fi

echo "4. 编译线程池示例..."
if g++ $CXX_FLAGS -o thread_pool_example thread_pool_example.cpp; then
    echo "   ✓ thread_pool_example 编译成功"
else
    echo "   ✗ thread_pool_example 编译失败"
    exit 1
fi

echo
echo "=== 所有程序编译完成 ==="
echo

# 询问是否运行示例
read -p "是否要运行所有示例程序? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "=== 运行示例程序 ==="
    
    echo
    echo ">>> 运行基础线程示例 <<<"
    echo "按 Enter 继续..."
    read
    ./basic_thread_example
    
    echo
    echo ">>> 运行Future和Promise示例 <<<"
    echo "按 Enter 继续..."
    read
    ./future_promise_example
    
    echo
    echo ">>> 运行线程同步示例 <<<"
    echo "按 Enter 继续..."
    read
    ./thread_synchronization
    
    echo
    echo ">>> 运行线程池示例 <<<"
    echo "按 Enter 继续..."
    read
    ./thread_pool_example
    
    echo
    echo "=== 所有示例运行完成 ==="
else
    echo
    echo "编译完成！你可以手动运行以下程序:"
    echo "  ./basic_thread_example"
    echo "  ./future_promise_example"
    echo "  ./thread_synchronization"
    echo "  ./thread_pool_example"
fi

echo
echo "=== 脚本执行完成 ==="
