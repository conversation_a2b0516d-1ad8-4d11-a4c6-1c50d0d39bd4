# C++ 并发编程示例

这个项目包含了一系列C++并发编程的示例代码，主要演示Thread和Future的使用，适合学习C++并发编程。

## 文件说明

### 1. `basic_thread_example.cpp`
**基础线程操作示例**
- 创建和启动基本线程
- 带参数的线程函数
- 使用类成员函数作为线程函数
- Lambda表达式作为线程函数
- 线程分离(detach)的使用
- 互斥锁保护共享资源

**主要概念:**
- `std::thread` 基本用法
- `thread.join()` 等待线程完成
- `thread.detach()` 分离线程
- `std::mutex` 和 `std::lock_guard` 保护共享资源

### 2. `future_promise_example.cpp`
**Future和Promise异步编程示例**
- `std::async` 异步任务执行
- 并行处理多个数据源
- `std::promise` 和 `std::future` 线程间通信
- 异步任务中的异常处理
- `std::packaged_task` 的使用

**主要概念:**
- `std::async` 启动异步任务
- `std::future` 获取异步结果
- `std::promise` 设置异步结果
- `std::packaged_task` 包装可调用对象
- 异步异常处理

### 3. `thread_synchronization.cpp`
**线程同步机制示例**
- 互斥锁(`std::mutex`)保护共享资源
- 生产者-消费者模式(`std::condition_variable`)
- 原子操作(`std::atomic`)
- 读写锁(`std::shared_mutex`)

**主要概念:**
- `std::mutex` 互斥锁
- `std::condition_variable` 条件变量
- `std::atomic` 原子操作
- `std::shared_mutex` 读写锁
- 生产者-消费者模式

### 4. `thread_pool_example.cpp`
**线程池实现和使用示例**
- 完整的线程池实现
- 任务队列管理
- 支持有返回值和无返回值的任务
- 使用`std::future`获取任务结果
- Lambda表达式任务
- 批量任务处理

**主要概念:**
- 线程池设计模式
- 任务队列
- `std::packaged_task` 包装任务
- `std::result_of` 类型推导
- 资源管理和清理

## 编译和运行

### 使用CMake编译
```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
make

# 运行示例
./bin/basic_thread_example
./bin/future_promise_example
./bin/thread_synchronization
./bin/thread_pool_example
```

### 直接使用g++编译
```bash
# 编译基础线程示例
g++ -std=c++17 -pthread -o basic_thread_example basic_thread_example.cpp

# 编译Future示例
g++ -std=c++17 -pthread -o future_promise_example future_promise_example.cpp

# 编译同步示例
g++ -std=c++17 -pthread -o thread_synchronization thread_synchronization.cpp

# 编译线程池示例
g++ -std=c++17 -pthread -o thread_pool_example thread_pool_example.cpp
```

## 学习建议

1. **按顺序学习**: 建议按照文件编号顺序学习，从基础概念开始
2. **动手实践**: 运行每个示例，观察输出结果
3. **修改实验**: 尝试修改参数，观察行为变化
4. **理解概念**: 重点理解每个同步原语的使用场景
5. **性能考虑**: 思考不同方法的性能影响

## 重要概念总结

### Thread相关
- **std::thread**: C++11引入的线程类
- **join()**: 等待线程完成
- **detach()**: 分离线程，让其在后台运行
- **get_id()**: 获取线程ID

### 同步原语
- **std::mutex**: 互斥锁，保护共享资源
- **std::lock_guard**: RAII风格的锁管理
- **std::unique_lock**: 更灵活的锁管理
- **std::condition_variable**: 条件变量，用于线程间通信
- **std::atomic**: 原子操作，无锁编程
- **std::shared_mutex**: 读写锁(C++17)

### 异步编程
- **std::async**: 启动异步任务
- **std::future**: 获取异步结果
- **std::promise**: 设置异步结果
- **std::packaged_task**: 包装可调用对象

## 注意事项

1. **编译器支持**: 需要支持C++17的编译器
2. **线程库**: 需要链接pthread库(-pthread)
3. **竞态条件**: 注意避免数据竞争
4. **死锁**: 小心锁的获取顺序
5. **异常安全**: 使用RAII管理资源

## 扩展学习

- 学习更多同步原语(如semaphore, barrier等)
- 了解无锁数据结构
- 研究并行算法
- 学习协程(C++20)
- 探索并行STL算法
