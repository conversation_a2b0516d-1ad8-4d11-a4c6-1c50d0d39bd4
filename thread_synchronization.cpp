#include <iostream>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <vector>
#include <chrono>
#include <atomic>

// 1. 互斥锁示例
class Counter {
private:
    int count = 0;
    std::mutex mtx;
    
public:
    void increment() {
        std::lock_guard<std::mutex> lock(mtx);
        ++count;
        std::cout << "计数器增加到: " << count << " (线程ID: " 
                  << std::this_thread::get_id() << ")\n";
    }
    
    int get_count() {
        std::lock_guard<std::mutex> lock(mtx);
        return count;
    }
};

// 2. 生产者-消费者模式
class ProducerConsumer {
private:
    std::queue<int> buffer;
    std::mutex buffer_mutex;
    std::condition_variable condition;
    const size_t max_size = 5;
    bool finished = false;
    
public:
    void produce(int id) {
        for (int i = 0; i < 10; ++i) {
            std::unique_lock<std::mutex> lock(buffer_mutex);
            
            // 等待缓冲区有空间
            condition.wait(lock, [this] { return buffer.size() < max_size; });
            
            int item = id * 100 + i;
            buffer.push(item);
            std::cout << "生产者 " << id << " 生产了: " << item 
                      << " (缓冲区大小: " << buffer.size() << ")\n";
            
            lock.unlock();
            condition.notify_all();
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    void consume(int id) {
        while (true) {
            std::unique_lock<std::mutex> lock(buffer_mutex);
            
            // 等待缓冲区有数据或生产完成
            condition.wait(lock, [this] { return !buffer.empty() || finished; });
            
            if (buffer.empty() && finished) {
                break;
            }
            
            if (!buffer.empty()) {
                int item = buffer.front();
                buffer.pop();
                std::cout << "消费者 " << id << " 消费了: " << item 
                          << " (缓冲区大小: " << buffer.size() << ")\n";
                
                lock.unlock();
                condition.notify_all();
                
                std::this_thread::sleep_for(std::chrono::milliseconds(150));
            }
        }
    }
    
    void set_finished() {
        std::lock_guard<std::mutex> lock(buffer_mutex);
        finished = true;
        condition.notify_all();
    }
};

// 3. 原子操作示例
std::atomic<int> atomic_counter(0);

void atomic_increment(int iterations) {
    for (int i = 0; i < iterations; ++i) {
        atomic_counter.fetch_add(1);
        // 或者简单地使用: ++atomic_counter;
    }
}

// 4. 读写锁示例 (使用 shared_mutex，C++17)
#include <shared_mutex>

class ReadWriteData {
private:
    std::string data = "初始数据";
    mutable std::shared_mutex rw_mutex;
    
public:
    // 读操作 - 可以并发
    std::string read_data() const {
        std::shared_lock<std::shared_mutex> lock(rw_mutex);
        std::cout << "读取数据: " << data << " (线程: " 
                  << std::this_thread::get_id() << ")\n";
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        return data;
    }
    
    // 写操作 - 独占
    void write_data(const std::string& new_data) {
        std::unique_lock<std::shared_mutex> lock(rw_mutex);
        std::cout << "写入数据: " << new_data << " (线程: " 
                  << std::this_thread::get_id() << ")\n";
        data = new_data;
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
};

int main() {
    std::cout << "=== C++ 线程同步示例 ===\n\n";
    
    // 1. 互斥锁示例
    std::cout << "1. 互斥锁保护共享资源:\n";
    Counter counter;
    std::vector<std::thread> counter_threads;
    
    for (int i = 0; i < 5; ++i) {
        counter_threads.emplace_back([&counter]() {
            for (int j = 0; j < 3; ++j) {
                counter.increment();
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        });
    }
    
    for (auto& t : counter_threads) {
        t.join();
    }
    
    std::cout << "最终计数: " << counter.get_count() << "\n\n";
    
    // 2. 生产者-消费者模式
    std::cout << "2. 生产者-消费者模式:\n";
    ProducerConsumer pc;
    
    std::thread producer1(&ProducerConsumer::produce, &pc, 1);
    std::thread producer2(&ProducerConsumer::produce, &pc, 2);
    std::thread consumer1(&ProducerConsumer::consume, &pc, 1);
    std::thread consumer2(&ProducerConsumer::consume, &pc, 2);
    
    producer1.join();
    producer2.join();
    
    // 通知消费者生产已完成
    pc.set_finished();
    
    consumer1.join();
    consumer2.join();
    
    std::cout << "\n3. 原子操作示例:\n";
    
    // 3. 原子操作
    std::vector<std::thread> atomic_threads;
    const int iterations_per_thread = 1000;
    
    for (int i = 0; i < 5; ++i) {
        atomic_threads.emplace_back(atomic_increment, iterations_per_thread);
    }
    
    for (auto& t : atomic_threads) {
        t.join();
    }
    
    std::cout << "原子计数器最终值: " << atomic_counter.load() 
              << " (期望值: " << 5 * iterations_per_thread << ")\n\n";
    
    // 4. 读写锁示例
    std::cout << "4. 读写锁示例:\n";
    ReadWriteData rw_data;
    std::vector<std::thread> rw_threads;
    
    // 创建多个读线程
    for (int i = 0; i < 3; ++i) {
        rw_threads.emplace_back([&rw_data, i]() {
            for (int j = 0; j < 2; ++j) {
                rw_data.read_data();
            }
        });
    }
    
    // 创建写线程
    rw_threads.emplace_back([&rw_data]() {
        rw_data.write_data("更新的数据1");
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        rw_data.write_data("更新的数据2");
    });
    
    // 再创建一个读线程
    rw_threads.emplace_back([&rw_data]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
        rw_data.read_data();
    });
    
    for (auto& t : rw_threads) {
        t.join();
    }
    
    std::cout << "\n=== 线程同步示例完成 ===\n";
    
    return 0;
}
